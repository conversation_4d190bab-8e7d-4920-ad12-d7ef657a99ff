tasks/active/TASKS_250908_02.md changed the structure of project and sector tables.
Mainly Tasks 2, 3 and 9 describe what has changed. The migrations are:
- packages/directus/migrations/20250909A-allow-null-project-localized-irrigation-controller.js
- packages/directus/migrations/20250909B-allow-null-sector-valve-controller.js
- packages/directus/migrations/20250909D-allow-null-project-irrigation-water-pump.js

There are some validation triggers that must be updated to reflect the new structure.
For example: check_sector_mesh is raising error when we set valve_controller to null.
Analyze the relevant  triggers and propose the necessary changes in the file tasks/analysis/250908_02.md
