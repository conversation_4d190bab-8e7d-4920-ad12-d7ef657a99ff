# Database Validation Triggers Analysis - Schema Changes Compatibility

**Analysis Date:** 2025-09-09  
**Related Task File:** `tasks/active/TASKS_250908_02.md`  
**Migration Files Analyzed:**

- `packages/directus/migrations/20250909A-allow-null-project-localized-irrigation-controller.js`
- `packages/directus/migrations/20250909B-allow-null-sector-valve-controller.js`
- `packages/directus/migrations/20250909D-allow-null-project-irrigation-water-pump.js`

## Executive Summary

The recent schema changes implemented in Tasks 2, 3, and 9 of `TASKS_250908_02.md` have introduced nullable foreign key fields to support device removal from properties. However, these changes have created incompatibilities with existing database validation triggers that enforce mesh network constraints. This analysis identifies all affected triggers and provides specific recommendations for updates.

## Schema Changes Summary

### Task 2: Project LIC Controller Nullability

- **Field:** `project.localized_irrigation_controller`
- **Change:** Modified from `NOT NULL` to `NULLABLE`
- **Purpose:** Allow projects to exist without an assigned LIC when the device is removed from the property

### Task 3: Sector Valve Controller Nullability

- **Fields:** `sector.valve_controller` and `sector.valve_controller_output`
- **Change:** Modified from `NOT NULL` to `NULLABLE`
- **Purpose:** Allow sectors to exist without assigned valve controllers when devices are removed
- **Additional Changes:** Updated unique constraint and check constraint to handle NULL values

### Task 9: Project Irrigation Water Pump Nullability

- **Field:** `project.irrigation_water_pump`
- **Change:** Modified from `NOT NULL` to `NULLABLE`
- **Purpose:** When LIC is null, irrigation water pump must also be null (mesh network dependency)

## Affected Database Validation Triggers

### 1. `check_sector_mesh()` Trigger - **CRITICAL INCOMPATIBILITY**

**Location:** `packages/directus/migrations/20250813A-mesh-constraints.js` (lines 100-132)

**Current Logic:**

```sql
-- Get Project's LIC property_device ID
SELECT pd.id INTO v_project_lic_property_device_id
FROM project p
JOIN property_device pd ON pd.device = p.localized_irrigation_controller AND pd.end_date IS NULL
WHERE p.id = NEW.project;

IF v_project_lic_property_device_id IS NOT NULL THEN
  -- Get Valve Controller's LIC property_device ID
  SELECT get_lic_for_device(NEW.valve_controller) INTO v_vc_lic_property_device_id;

  IF v_project_lic_property_device_id IS DISTINCT FROM v_vc_lic_property_device_id THEN
    RAISE EXCEPTION 'Valve Controller must be in the same mesh network as the project LIC.';
  END IF;
END IF;
```

**Problem:**

- When `project.localized_irrigation_controller` is NULL, the JOIN fails and `v_project_lic_property_device_id` becomes NULL
- When `sector.valve_controller` is NULL, `get_lic_for_device(NEW.valve_controller)` is called with NULL, which may cause issues
- The trigger doesn't properly handle the case where both LIC and valve controller are NULL (which should be valid)

**Required Changes:**

1. Add explicit NULL checks for `NEW.valve_controller`
2. Modify logic to allow NULL valve controllers when project LIC is also NULL
3. Only enforce mesh network constraint when both devices are present

### 2. `check_project_mesh()` Trigger - **MODERATE INCOMPATIBILITY**

**Location:** `packages/directus/migrations/20250813A-mesh-constraints.js` (lines 54-97)

**Current Logic:**

```sql
IF NEW.localized_irrigation_controller IS NOT NULL THEN
  -- Get the LIC's property_device ID
  SELECT id INTO v_project_lic_property_device_id
  FROM property_device
  WHERE device = NEW.localized_irrigation_controller AND end_date IS NULL;

  -- Check irrigation pump
  IF NEW.irrigation_water_pump IS NOT NULL THEN
    SELECT get_lic_for_device(wp.water_pump_controller) INTO v_irrigation_pump_lic_property_device_id
    FROM water_pump wp WHERE wp.id = NEW.irrigation_water_pump;
    IF v_project_lic_property_device_id IS DISTINCT FROM v_irrigation_pump_lic_property_device_id THEN
      RAISE EXCEPTION 'Irrigation pump must be in the same mesh network as the project LIC.';
    END IF;
  END IF;
  -- Similar logic for fertigation pump...
END IF;
```

**Problem:**

- The trigger correctly handles NULL `localized_irrigation_controller` by wrapping checks in an IF statement
- However, it doesn't enforce the business rule that `irrigation_water_pump` should be NULL when `localized_irrigation_controller` is NULL
- Missing validation for the inverse: if `irrigation_water_pump` is NOT NULL, then `localized_irrigation_controller` should also be NOT NULL

**Required Changes:**

1. Add validation to ensure `irrigation_water_pump` is NULL when `localized_irrigation_controller` is NULL
2. Add validation to ensure `localized_irrigation_controller` is NOT NULL when `irrigation_water_pump` is NOT NULL

### 3. `check_reservoir_mesh()` Trigger - **NO CHANGES REQUIRED**

**Location:** `packages/directus/migrations/20250813A-mesh-constraints.js` (lines 21-51)

**Status:** ✅ **Compatible** - No changes needed

**Reason:** This trigger only validates reservoir devices (`reservoir_monitor` and `water_pump`), which were not affected by the schema changes. The logic already properly handles NULL values.

## Additional Validation Logic Affected

### 4. Frontend Validation - `ProjectConfigPanel.tsx`

**Location:** `packages/app/src/pages/main/components/ProjectConfigPanel.tsx` (lines 63-71)

**Current Logic:**

```typescript
const isValid = () => {
  const hasName = formData.name.trim() !== "";
  // Water pump is only required if there's an irrigation controller
  const hasRequiredPump = formData.irrigationControllerId
    ? formData.waterPumpId !== "" && formData.waterPumpId !== null
    : true; // Not required when no LIC is selected

  return hasName && hasRequiredPump;
};
```

**Status:** ✅ **Already Compatible** - The frontend validation was already updated to handle the nullable relationship correctly.

### 5. MQTT Integration Validation

**Location:** `packages/mqtt-integration/src/irriganet/db-loader/validation.ts` (lines 105-123)

**Current Logic:**

```typescript
export function validateProjectIrrigationPump(
  project: ProjectWithSchedulingData
): void {
  if (!project.irrigation_water_pump) {
    throw new MissingPropertyError(
      "irrigation_water_pump",
      "project",
      project.id
    );
  }
  // Additional validation...
}
```

**Problem:** This validation function still requires `irrigation_water_pump` to be present, which conflicts with the new nullable schema.

**Required Changes:**

1. Update validation to allow NULL `irrigation_water_pump` when `localized_irrigation_controller` is also NULL
2. Maintain validation when LIC is present but irrigation pump is missing

## Recommended Trigger Updates

### 1. Updated `check_sector_mesh()` Function

```sql
CREATE OR REPLACE FUNCTION check_sector_mesh()
RETURNS TRIGGER AS $$
DECLARE
  v_project_lic_property_device_id UUID;
  v_vc_lic_property_device_id UUID;
BEGIN
  -- Only validate if valve_controller is not NULL
  IF NEW.valve_controller IS NOT NULL THEN
    -- Get Project's LIC property_device ID
    SELECT pd.id INTO v_project_lic_property_device_id
    FROM project p
    JOIN property_device pd ON pd.device = p.localized_irrigation_controller AND pd.end_date IS NULL
    WHERE p.id = NEW.project;

    -- If project has a LIC, validate mesh network constraint
    IF v_project_lic_property_device_id IS NOT NULL THEN
      -- Get Valve Controller's LIC property_device ID
      SELECT get_lic_for_device(NEW.valve_controller) INTO v_vc_lic_property_device_id;

      IF v_project_lic_property_device_id IS DISTINCT FROM v_vc_lic_property_device_id THEN
        RAISE EXCEPTION 'Valve Controller must be in the same mesh network as the project LIC.';
      END IF;
    ELSE
      -- If project has no LIC, valve controller should not be assigned
      RAISE EXCEPTION 'Cannot assign valve controller to sector when project has no LIC assigned.';
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### 2. Updated `check_project_mesh()` Function

```sql
CREATE OR REPLACE FUNCTION check_project_mesh()
RETURNS TRIGGER AS $$
DECLARE
  v_project_lic_property_device_id UUID;
  v_irrigation_pump_lic_property_device_id UUID;
  v_fertigation_pump_lic_property_device_id UUID;
BEGIN
  -- Validate irrigation_water_pump dependency on localized_irrigation_controller
  IF NEW.localized_irrigation_controller IS NULL AND NEW.irrigation_water_pump IS NOT NULL THEN
    RAISE EXCEPTION 'irrigation_water_pump must be NULL when localized_irrigation_controller is NULL.';
  END IF;

  -- If LIC is assigned, validate mesh network constraints
  IF NEW.localized_irrigation_controller IS NOT NULL THEN
    -- Get the LIC's property_device ID
    SELECT id INTO v_project_lic_property_device_id
    FROM property_device
    WHERE device = NEW.localized_irrigation_controller AND end_date IS NULL;

    -- Check irrigation pump
    IF NEW.irrigation_water_pump IS NOT NULL THEN
      SELECT get_lic_for_device(wp.water_pump_controller) INTO v_irrigation_pump_lic_property_device_id
      FROM water_pump wp WHERE wp.id = NEW.irrigation_water_pump;
      IF v_project_lic_property_device_id IS DISTINCT FROM v_irrigation_pump_lic_property_device_id THEN
        RAISE EXCEPTION 'Irrigation pump must be in the same mesh network as the project LIC.';
      END IF;
    END IF;

    -- Check fertigation pump
    IF NEW.fertigation_water_pump IS NOT NULL THEN
      SELECT get_lic_for_device(wp.water_pump_controller) INTO v_fertigation_pump_lic_property_device_id
      FROM water_pump wp WHERE wp.id = NEW.fertigation_water_pump;
      IF v_project_lic_property_device_id IS DISTINCT FROM v_fertigation_pump_lic_property_device_id THEN
        RAISE EXCEPTION 'Fertigation pump must be in the same mesh network as the project LIC.';
      END IF;
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

## Implementation Priority

### High Priority (Immediate Action Required)

1. **`check_sector_mesh()` trigger** - Currently causing errors when valve_controller is NULL
2. **`check_project_mesh()` trigger** - Missing validation for irrigation_water_pump dependency

### Medium Priority (Should be addressed soon)

3. **MQTT Integration validation** - Update `validateProjectIrrigationPump()` function
4. **Documentation updates** - Update DDL.md and mesh network documentation

### Low Priority (Future consideration)

5. **Additional frontend validation** - Ensure all UI components handle nullable fields correctly
6. **Test coverage** - Add comprehensive tests for NULL scenarios

## Risk Assessment

### High Risk

- **Data Integrity:** Current trigger incompatibilities could allow invalid data states
- **System Errors:** NULL valve controllers causing trigger exceptions during normal operations

### Medium Risk

- **Business Logic Violations:** Missing validation could allow irrigation pumps without LIC controllers
- **User Experience:** Confusing error messages when triggers fail unexpectedly

### Low Risk

- **Performance:** Updated triggers may have minimal performance impact
- **Backward Compatibility:** Changes are additive and shouldn't break existing valid data

## Detailed Impact Analysis

### Database Layer Impacts

1. **Trigger Execution Failures**

   - `check_sector_mesh()` will fail when `NEW.valve_controller` is NULL
   - Potential for `get_lic_for_device()` function to receive NULL parameters unexpectedly

2. **Data Consistency Issues**

   - Projects with NULL LIC but non-NULL irrigation pumps could be created
   - Sectors with NULL valve controllers but non-NULL valve_controller_output could exist

3. **Constraint Violations**
   - Unique constraints on sectors may behave differently with NULL values
   - Check constraints updated but triggers not aligned

### Application Layer Impacts

1. **Frontend Components**

   - Most components already handle nullable fields correctly
   - ProjectConfigPanel validation is compatible
   - Device removal flow properly sets fields to NULL

2. **Backend Services**

   - MQTT integration validation needs updates
   - Device message request validation may need review
   - Scheduling logic should handle NULL device assignments

3. **API Responses**
   - Directus API will return NULL values for removed devices
   - Frontend must handle NULL device references in all contexts

## Testing Scenarios Required

### Database Trigger Testing

1. **Sector Operations**

   - Insert sector with NULL valve_controller ✓ Should succeed
   - Insert sector with valve_controller but project has NULL LIC ✗ Should fail
   - Update sector to set valve_controller to NULL ✓ Should succeed
   - Update sector valve_controller when project LIC changes ✓ Should validate mesh

2. **Project Operations**

   - Insert project with NULL LIC and NULL irrigation_water_pump ✓ Should succeed
   - Insert project with NULL LIC but non-NULL irrigation_water_pump ✗ Should fail
   - Insert project with LIC but NULL irrigation_water_pump ✓ Should succeed
   - Update project to remove LIC without clearing irrigation_water_pump ✗ Should fail

3. **Device Removal Scenarios**
   - Remove LIC from property → project.localized_irrigation_controller set to NULL
   - Remove valve controller from property → sector.valve_controller set to NULL
   - Remove water pump controller → project.irrigation_water_pump set to NULL

### Integration Testing

1. **MQTT Message Processing**

   - Process messages for projects with NULL irrigation pumps
   - Handle scheduling requests for sectors with NULL valve controllers
   - Validate device state updates when devices are removed

2. **Frontend User Flows**
   - Create project without LIC assignment
   - Create sector without valve controller assignment
   - Remove devices and verify UI handles NULL states correctly

## Migration Strategy

### Phase 1: Critical Fixes (Immediate)

1. Create migration `20250909F-fix-mesh-constraint-triggers.js`
2. Update `check_sector_mesh()` function
3. Update `check_project_mesh()` function
4. Test trigger updates in development environment

### Phase 2: Application Updates (Next Sprint)

1. Update MQTT integration validation functions
2. Review and update any remaining frontend validation
3. Add comprehensive test coverage for NULL scenarios

### Phase 3: Documentation and Monitoring (Following Sprint)

1. Update DDL.md with new trigger logic
2. Update mesh network documentation
3. Add monitoring for trigger exceptions
4. Create troubleshooting guide for NULL device scenarios

## Proposed Migration File Structure

```javascript
// packages/directus/migrations/20250909F-fix-mesh-constraint-triggers.js
export async function up(knex) {
  // Update check_sector_mesh function
  await knex.raw(`CREATE OR REPLACE FUNCTION check_sector_mesh() ...`);

  // Update check_project_mesh function
  await knex.raw(`CREATE OR REPLACE FUNCTION check_project_mesh() ...`);
}

export async function down(knex) {
  // Restore original trigger functions
  // (Copy from 20250813A-mesh-constraints.js)
}
```

## Next Steps

1. **Create migration file** to update the affected trigger functions
2. **Test trigger updates** with various NULL/NOT NULL combinations
3. **Update MQTT integration validation** to handle nullable irrigation pumps
4. **Update documentation** to reflect new validation rules
5. **Add comprehensive test cases** for all NULL scenarios

## Conclusion

The schema changes successfully enable device removal functionality, but require corresponding updates to database validation triggers. The most critical issue is the `check_sector_mesh()` trigger, which needs immediate attention to prevent runtime errors. The recommended updates maintain data integrity while supporting the new nullable field requirements.

The analysis reveals that while the schema changes are well-designed, the validation layer needs to be updated to maintain system integrity. The proposed trigger updates will ensure that:

1. **Data consistency** is maintained across device assignments
2. **Mesh network constraints** are properly enforced when devices are present
3. **NULL values** are handled gracefully throughout the system
4. **Business rules** regarding device dependencies are preserved

Implementation of these changes should be prioritized to prevent production issues and ensure the device removal functionality works correctly.
