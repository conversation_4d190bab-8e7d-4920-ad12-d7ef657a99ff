import type { AUTSector } from "@/api/queries/account";
import { Modal, Tabs } from "@/components";
import {
  createProject<PERSON>tom,
  createSector<PERSON>tom,
  devicesAtom,
  projectsAtom,
  refetchData<PERSON>tom,
  selectedProperty<PERSON>tom,
  updateProject<PERSON>tom,
  updateSector<PERSON>tom,
  waterPumpsAtom,
} from "@/store";
import { deleteSector<PERSON>tom } from "@/store/crud";
import {
  deviceToLicMapAtom,
  projectByIdAtom,
  propertyDevicesAtom,
  sectorsByProjectIdAtom,
} from "@/store/data";
import { useAtomValue, useSetAtom } from "jotai";
import { ExternalLink, Map as MapIcon, Settings } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { useLocation, useSearchParams } from "wouter";
import ProjectConfigPanel from "./components/ProjectConfigPanel";
import SectorDetailPanel from "./components/SectorDetailPanel";
import SectorsPanel from "./components/SectorsPanel";

// Form interfaces for the component
interface ProjectConfigForm {
  id: string;
  name: string;
  description: string;
  start_date: string;
  end_date: string;
  irrigation_water_pump: string | null;
  fertigation_water_pump: string;
  localized_irrigation_controller: string | null;
  pipe_wash_time_seconds: string; // in seconds (stored as string to allow empty values)
  backwash_duration_seconds: number | null; // in seconds
  backwash_period_seconds: number | null; // in seconds
  backwash_pump_type: "IRRIGATION" | "FERTIGATION" | "";
}

interface SectorForm {
  id: string;
  project: string;
  name: string;
  description: string;
  valve_controller: string | null;
  valve_controller_output: number | null; // 1, 2, 3, or 4
  area: number;
  power: number;
}

// Define tabs configuration with full type safety
const PROJECT_CONFIG_TABS = [
  { key: "config", label: "Configuração", icon: Settings },
  { key: "sectors", label: "Setores", icon: MapIcon },
] as const;

export type ProjectConfigPageProps = {
  projectId: string;
  activeTab: string;
};

function ProjectConfigPage({
  projectId,
  activeTab: initialActiveTab,
}: ProjectConfigPageProps) {
  if (
    initialActiveTab &&
    !PROJECT_CONFIG_TABS.some((tab) => tab.key === initialActiveTab)
  ) {
    initialActiveTab = "config"; // Default to config if invalid
  }
  const [activeTab, setActiveTab] = useState(initialActiveTab || "config");

  const [, setLocation] = useLocation();

  const projectById = useAtomValue(projectByIdAtom);
  const sectorsByProjectId = useAtomValue(sectorsByProjectIdAtom);

  const project = useMemo(() => {
    return projectById(projectId);
  }, [projectById, projectId]);
  const property = useAtomValue(selectedPropertyAtom);
  const sectors = useMemo(() => {
    return sectorsByProjectId(projectId);
  }, [sectorsByProjectId, projectId]);
  const propertyDevices = useAtomValue(propertyDevicesAtom);
  const devices = useAtomValue(devicesAtom);
  const waterPumps = useAtomValue(waterPumpsAtom);
  const deviceToLicMap = useAtomValue(deviceToLicMapAtom);

  // CRUD operations
  const createProject = useSetAtom(createProjectAtom);
  const updateProject = useSetAtom(updateProjectAtom);
  const createSector = useSetAtom(createSectorAtom);
  const updateSector = useSetAtom(updateSectorAtom);
  const deleteSector = useSetAtom(deleteSectorAtom);
  const refetchData = useSetAtom(refetchDataAtom);

  const [saving, setSaving] = useState(false);
  const [showSectorDetail, setShowSectorDetail] = useState(false);
  const [editingSector, setEditingSector] = useState<AUTSector | undefined>(
    undefined
  );

  const [formData, setFormData] = useState<ProjectConfigForm>({
    id: "",
    name: "",
    description: "",
    start_date: "",
    end_date: "",
    irrigation_water_pump: null,
    fertigation_water_pump: "",
    localized_irrigation_controller: null,
    pipe_wash_time_seconds: "",
    backwash_duration_seconds: null,
    backwash_period_seconds: null,
    backwash_pump_type: "",
  });

  const isNewProject = projectId === "new";

  // Check if the project's irrigation pump has frequency inverter
  const hasFrequencyInverter = useMemo(() => {
    if (
      !project ||
      (!project.irrigation_water_pump && !formData.irrigation_water_pump)
    )
      return false;

    // Use current project data or form data for irrigation pump
    const irrigationPumpId =
      project?.irrigation_water_pump || formData.irrigation_water_pump;
    if (!irrigationPumpId) return false;

    const irrigationPump = waterPumps.find(
      (pump) => pump.id === irrigationPumpId
    );
    return irrigationPump?.has_frequency_inverter || false;
  }, [project, formData.irrigation_water_pump, waterPumps]);

  // Create a map from a water pump ID to its LIC's device ID
  const pumpToLicMap = useMemo(() => {
    const map = new Map<string, string | null>();
    waterPumps.forEach((pump) => {
      const controller = pump.water_pump_controller;
      let controllerId: string | null = null;
      if (typeof controller === "object" && controller !== null) {
        controllerId = (controller as any).id;
      } else if (typeof controller === "string") {
        controllerId = controller;
      }

      if (pump.id && controllerId) {
        map.set(pump.id, deviceToLicMap.get(controllerId) ?? null);
      }
    });
    return map;
  }, [waterPumps, deviceToLicMap]);

  useEffect(() => {
    if (isNewProject) {
      // For new projects, keep form data empty
      setFormData({
        id: "",
        name: "",
        description: "",
        start_date: "",
        end_date: "",
        irrigation_water_pump: null,
        fertigation_water_pump: "",
        localized_irrigation_controller: null,
        pipe_wash_time_seconds: "",
        backwash_duration_seconds: null,
        backwash_period_seconds: null,
        backwash_pump_type: "",
      });
    } else if (project) {
      // For existing projects, populate form with project data
      setFormData({
        id: project.id,
        name: project.name,
        description: project.description || "",
        start_date: project.start_date || "",
        end_date: project.end_date || "",
        irrigation_water_pump: project.irrigation_water_pump || null,
        fertigation_water_pump: project.fertigation_water_pump || "",
        localized_irrigation_controller:
          project.localized_irrigation_controller,
        pipe_wash_time_seconds:
          project.pipe_wash_time_seconds?.toString() || "",
        backwash_duration_seconds: project.backwash_duration_seconds,
        backwash_period_seconds: project.backwash_period_seconds,
        backwash_pump_type: project.backwash_pump_type || "",
      });
    }
  }, [isNewProject, project]);

  const handleInputChange = (field: keyof ProjectConfigForm, value: string) => {
    setFormData((prev) => {
      const newState = { ...prev, [field]: value };

      // When LIC changes, clear the pump selections if they don't match
      if (field === "localized_irrigation_controller") {
        const selectedLicId = value;
        const irrigationPumpId = newState.irrigation_water_pump;
        const fertigationPumpId = newState.fertigation_water_pump;

        if (
          irrigationPumpId &&
          pumpToLicMap.get(irrigationPumpId) !== selectedLicId
        ) {
          newState.irrigation_water_pump = null;
        }
        if (
          fertigationPumpId &&
          pumpToLicMap.get(fertigationPumpId) !== selectedLicId
        ) {
          newState.fertigation_water_pump = "";
        }
      }

      return newState;
    });
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      if (isNewProject) {
        // Create new project
        const projectData = {
          property: property?.id, // Add the required property field
          name: formData.name,
          description: formData.description || null,
          start_date: formData.start_date || null,
          end_date: formData.end_date || null,
          irrigation_water_pump: formData.irrigation_water_pump || null,
          fertigation_water_pump: formData.fertigation_water_pump || undefined,
          localized_irrigation_controller:
            formData.localized_irrigation_controller ?? undefined,
          pipe_wash_time_seconds:
            formData.pipe_wash_time_seconds.trim() !== ""
              ? parseInt(formData.pipe_wash_time_seconds)
              : null,
          backwash_duration_seconds: formData.backwash_duration_seconds
            ? formData.backwash_duration_seconds
            : null,
          backwash_period_seconds: formData.backwash_period_seconds
            ? formData.backwash_period_seconds
            : null,
          backwash_pump_type: formData.backwash_pump_type || null,
        };

        const result = await createProject(projectData);
        if (result.success && result.data?.id) {
          setLocation(`/app/projects/${result.data.id}/config`);
        }
      } else {
        // Update existing project
        const projectData = {
          name: formData.name,
          description: formData.description || null,
          start_date: formData.start_date || null,
          end_date: formData.end_date || null,
          irrigation_water_pump: formData.irrigation_water_pump || null,
          fertigation_water_pump: formData.fertigation_water_pump || undefined,
          localized_irrigation_controller:
            formData.localized_irrigation_controller ?? undefined,
          pipe_wash_time_seconds:
            formData.pipe_wash_time_seconds.trim() !== ""
              ? parseInt(formData.pipe_wash_time_seconds)
              : null,
          backwash_duration_seconds: formData.backwash_duration_seconds
            ? formData.backwash_duration_seconds
            : null,
          backwash_period_seconds: formData.backwash_period_seconds
            ? formData.backwash_period_seconds
            : null,
          backwash_pump_type: formData.backwash_pump_type || null,
        };

        const result = await updateProject({
          id: formData.id,
          data: projectData,
        });
        if (result.success) {
          setLocation(`/app/projects/${projectId}`);
        }
      }
    } catch (error) {
      console.error("Error saving project config:", error);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (isNewProject) {
      setLocation("/app/projects");
    } else {
      setLocation(`/app/projects/${projectId}`);
    }
  };

  const handleEditSector = (sectorId: string) => {
    const sector = sectors?.find((s) => s.id === sectorId);
    setEditingSector(sector);
    setShowSectorDetail(true);
  };

  const handleAddSector = () => {
    setEditingSector(undefined);
    setShowSectorDetail(true);
  };

  const handleSaveSector = async (sectorData: Omit<SectorForm, "id">) => {
    try {
      if (editingSector) {
        // Update existing sector
        const result = await updateSector({
          id: editingSector.id,
          data: {
            name: sectorData.name,
            description: sectorData.description || null,
            valve_controller: sectorData.valve_controller,
            valve_controller_output: sectorData.valve_controller_output as
              | 1
              | 2
              | 3
              | 4,
            area: sectorData.area,
            power: sectorData.power,
          },
        });
        if (result.success) {
          await refetchData();
        }
      } else {
        // Add new sector
        const result = await createSector({
          project: projectId!,
          name: sectorData.name,
          description: sectorData.description || null,
          valve_controller: sectorData.valve_controller,
          valve_controller_output: sectorData.valve_controller_output as
            | 1
            | 2
            | 3
            | 4,
          area: sectorData.area,
          power: sectorData.power,
        });
        if (result.success) {
          await refetchData();
        }
      }
      setShowSectorDetail(false);
      setEditingSector(undefined);
    } catch (error) {
      console.error("Error saving sector:", error);
    }
  };

  const handleDeleteSector = async (sectorId: string) => {
    deleteSector(sectorId);
    setShowSectorDetail(false);
  };

  const handleCancelSectorEdit = () => {
    setShowSectorDetail(false);
    setEditingSector(undefined);
  };

  // Get all projects to check for assignments - MOVED BEFORE CONDITIONAL RETURN
  const allProjects = useAtomValue(projectsAtom);

  // Transform all devices for sectors (VC devices)
  const transformedAllDevices =
    propertyDevices?.map(({ device, metadata }) => ({
      id: device.id,
      name: device.identifier, // Use identifier as name since name doesn't exist in API
      model: device.model,
      identifier: device.identifier,
      label: metadata?.label as string | undefined,
    })) || [];

  // Transform and filter LIC devices for project irrigation controllers
  const transformedLICDevices =
    propertyDevices
      ?.filter(({ device }) => device.model === "LIC") // Only LIC devices for irrigation controllers
      ?.map(({ device, metadata }) => ({
        id: device.id,
        name: device.identifier, // Use identifier as name since name doesn't exist in API
        model: device.model,
        identifier: device.identifier,
        label: metadata?.label as string | undefined,
      })) || [];

  // Filter available LIC devices (not assigned to other projects)
  const availableLICDevices = transformedLICDevices;

  // Transform and filter water pumps
  const transformedWaterPumps =
    waterPumps?.map((pump) => ({
      id: pump.id,
      label: pump.label,
      identifier: pump.identifier,
      pumpType: pump.pump_type,
      pumpModel: pump.pump_model,
      controller:
        devices.find((d) => d.id === pump.water_pump_controller) || null,
    })) || [];

  // Filter available irrigation water pumps (IRRIGATION type, not assigned to other projects)
  const availableIrrigationPumps = useMemo(() => {
    return transformedWaterPumps.filter((pump) => {
      if (pump.pumpType !== "IRRIGATION") return false;

      // Check if this pump is assigned to any other project as irrigation pump
      const isAssignedToOtherProject = allProjects.some((proj: any) => {
        if (proj.id === projectId) return false; // Exclude current project
        const assignedPumpId =
          typeof proj.irrigation_water_pump === "object"
            ? proj.irrigation_water_pump?.id
            : proj.irrigation_water_pump;
        return assignedPumpId === pump.id;
      });
      if (isAssignedToOtherProject) return false;

      // Mesh network check
      const selectedLicId = formData.localized_irrigation_controller;
      if (selectedLicId) {
        const pumpLicId = pumpToLicMap.get(pump.id);
        return pumpLicId === selectedLicId;
      }

      return true; // Show if no LIC is selected yet
    });
  }, [
    transformedWaterPumps,
    allProjects,
    projectId,
    formData.localized_irrigation_controller,
    pumpToLicMap,
  ]);

  // Filter available fertigation water pumps (FERTIGATION type, not assigned to other projects)
  const availableFertigationPumps = useMemo(() => {
    return transformedWaterPumps.filter((pump) => {
      if (pump.pumpType !== "FERTIGATION") return false;

      // Check if this pump is assigned to any other project as fertigation pump
      const isAssignedToOtherProject = allProjects.some((proj: any) => {
        if (proj.id === projectId) return false; // Exclude current project
        const assignedPumpId =
          typeof proj.fertigation_water_pump === "object"
            ? proj.fertigation_water_pump?.id
            : proj.fertigation_water_pump;
        return assignedPumpId === pump.id;
      });
      if (isAssignedToOtherProject) return false;

      // Mesh network check
      const selectedLicId = formData.localized_irrigation_controller;
      if (selectedLicId) {
        const pumpLicId = pumpToLicMap.get(pump.id);
        return pumpLicId === selectedLicId;
      }

      return true; // Show if no LIC is selected yet
    });
  }, [
    transformedWaterPumps,
    allProjects,
    projectId,
    formData.localized_irrigation_controller,
    pumpToLicMap,
  ]);

  // Show project not found for edit mode when no project is available
  if (!isNewProject && !project) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-neutral-900 mb-2">
            Projeto não encontrado
          </h2>
          <p className="text-neutral-600">
            O projeto solicitado não foi encontrado ou você não tem permissão
            para acessá-lo.
          </p>
        </div>
      </div>
    );
  }

  // // For fertigation pumps, we'll filter water pumps that are used for fertigation
  // const fertigationPumps = transformedWaterPumps.filter(
  //   (pump) =>
  //     pump.pumpType.toLowerCase().includes("fertigation") ||
  //     pump.pumpType.toLowerCase().includes("dosing")
  // );

  const transformedSectors =
    sectors?.map((sector) => ({
      id: sector.id,
      projectId: sector.project, // Now always a string
      name: sector.name,
      description: sector.description || "",
      controllerId: sector.valve_controller, // Now always a string
      controllerOutput: sector.valve_controller_output,
      area: sector.area || 0,
      power: sector.power,
    })) || [];

  // Transform form data for child components
  const transformedFormData = {
    id: formData.id,
    name: formData.name,
    description: formData.description,
    startDate: formData.start_date,
    endDate: formData.end_date,
    irrigationControllerId: formData.localized_irrigation_controller,
    waterPumpId: formData.irrigation_water_pump || "",
    fertigationPumpId: formData.fertigation_water_pump,
    pipeWashTimeMinutes:
      formData.pipe_wash_time_seconds.trim() !== ""
        ? Math.round(parseInt(formData.pipe_wash_time_seconds) / 60)
        : null,
    // Note: backwash fields are now read-only and come from global property
    backwashDurationMinutes: null,
    backwashPeriodMinutes: null,
    backwashPumpType: formData.backwash_pump_type,
  };

  const transformedEditingSector = editingSector
    ? {
        id: editingSector.id,
        projectId: editingSector.project, // Now always a string
        name: editingSector.name,
        description: editingSector.description || "",
        controllerId: editingSector.valve_controller, // Now always a string
        controllerOutput: editingSector.valve_controller_output,
        area: editingSector.area || 0,
        power: editingSector.power,
      }
    : undefined;

  return (
    <div className="flex flex-col h-full">
      {/* Fixed Header */}
      <div className="sticky top-0 bg-white z-10 border-b border-neutral-100 px-6 py-4 min-h-14">
        {/* Project Title */}
        <h4 className="text-lg font-medium text-neutral-900 mb-4 flex justify-between">
          <div>
            <span className="">
              {isNewProject ? "Novo Projeto" : "Projeto"}
            </span>{" "}
            {!isNewProject && (
              <span className="font-semibold text-neutral-900">
                {formData.name}
              </span>
            )}
          </div>
          {!isNewProject && (
            <button
              onClick={() => {
                setLocation(`/app/projects/${projectId}`);
              }}
              className="ml-4 px-3 py-1.5 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg border border-blue-200 transition-all duration-200 flex items-center gap-1.5"
            >
              Planejamento
              <ExternalLink className="w-4 h-4" />
            </button>
          )}
        </h4>
        {/* Top Tab Navigation */}
        <div className="mb-0">
          <Tabs
            tabs={
              isNewProject
                ? PROJECT_CONFIG_TABS.slice(0, 1)
                : PROJECT_CONFIG_TABS
            }
            onChange={setActiveTab}
            value={isNewProject ? "config" : activeTab}
            searchParamsKey="activeTab"
            className="bg-neutral-100 rounded-xl"
          />
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto ">
        {/* Tab Content */}
        {activeTab === "config" ? (
          <ProjectConfigPanel
            formData={transformedFormData}
            property={property}
            devices={availableLICDevices}
            waterPumps={availableIrrigationPumps}
            fertigationPumps={availableFertigationPumps}
            saving={saving}
            onInputChange={(field, value) => {
              // Map the old field names to new field names
              const fieldMap: Record<string, keyof ProjectConfigForm> = {
                startDate: "start_date",
                endDate: "end_date",
                irrigationControllerId: "localized_irrigation_controller",
                waterPumpId: "irrigation_water_pump",
                fertigationPumpId: "fertigation_water_pump",
                pipeWashTimeMinutes: "pipe_wash_time_seconds",
                backwashPumpType: "backwash_pump_type",
                // Note: backwash fields are now read-only global settings
              };
              const mappedField =
                fieldMap[field] || (field as keyof ProjectConfigForm);
              // Convert value to seconds if needed (only for pipe wash time)
              // Handle empty strings properly - don't convert to 0
              if (field === "pipeWashTimeMinutes") {
                if (value.trim() === "") {
                  value = ""; // Keep as empty string
                } else {
                  const numValue = Number(value);
                  value = isNaN(numValue) ? "" : (numValue * 60).toString();
                }
              }

              // Handle fertigation pump change - clear backwash pump type if FERTIGATION is selected but no fertigation pump
              if (field === "fertigationPumpId" && !value) {
                // If fertigation pump is being cleared, also clear backwash pump type if it was FERTIGATION
                if (formData.backwash_pump_type === "FERTIGATION") {
                  handleInputChange("backwash_pump_type", "");
                }
              }

              // Handle backwash pump type change - prevent FERTIGATION if no fertigation pump
              if (
                field === "backwashPumpType" &&
                value === "FERTIGATION" &&
                !formData.fertigation_water_pump
              ) {
                return; // Don't allow FERTIGATION selection if no fertigation pump
              }

              handleInputChange(mappedField, value);
            }}
            onSave={handleSave}
            onCancel={handleCancel}
          />
        ) : (
          <SectorsPanel
            sectors={transformedSectors}
            devices={transformedAllDevices}
            hasFrequencyInverter={hasFrequencyInverter}
            onEditSector={handleEditSector}
            onAddSector={handleAddSector}
          />
        )}

        {/* Sector Detail Modal */}
        <Modal
          title={editingSector ? "Editar Setor" : "Adicionar Setor"}
          isOpen={showSectorDetail}
          onClose={handleCancelSectorEdit}
          showCloseButton={true}
        >
          <SectorDetailPanel
            sector={transformedEditingSector}
            projectId={projectId || ""}
            devices={transformedAllDevices}
            _sectors={transformedSectors}
            onSave={(sectorData) => {
              // Transform back to API format
              const transformedSectorData: Omit<SectorForm, "id"> = {
                project: sectorData.projectId,
                name: sectorData.name,
                description: sectorData.description,
                valve_controller: sectorData.controllerId,
                valve_controller_output: sectorData.controllerOutput,
                area: sectorData.area,
                power: sectorData.power,
              };
              handleSaveSector(transformedSectorData);
            }}
            onCancel={handleCancelSectorEdit}
            onDelete={(sectorId) => {
              handleDeleteSector(sectorId);
            }}
          />
        </Modal>
      </div>
    </div>
  );
}

export default ProjectConfigPage;
