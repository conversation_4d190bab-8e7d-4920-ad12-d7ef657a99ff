import { Tabs } from "@/components";
import { <PERSON>pu, Wrench } from "lucide-react";

const HARDWARE_TABS = [
  { key: "devices", label: "Dispositivos", icon: Cpu },
  { key: "pumps", label: "Bomb<PERSON>", icon: Wrench },
] as const;

interface HardwareTabsProps {
  activeTab: (typeof HARDWARE_TABS)[number]["key"];
  onTabChange: (tab: (typeof HARDWARE_TABS)[number]["key"]) => void;
}

export default function HardwareTabs({
  activeTab,
  onTabChange,
}: HardwareTabsProps) {
  return (
    <Tabs
      tabs={HARDWARE_TABS}
      value={activeTab}
      onChange={onTabChange}
      className="mb-6"
      searchParamsKey="activeTab"
    />
  );
}
