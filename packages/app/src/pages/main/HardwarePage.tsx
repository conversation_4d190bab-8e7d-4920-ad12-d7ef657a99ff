import type { AUTWaterPump } from "@/api/queries/account";
import { appShell, propertyDevicesAtom, selectedProper<PERSON><PERSON><PERSON> } from "@/store";
import { useAtomValue, useSetAtom } from "jotai";
import { useEffect, useMemo } from "react";
import type { DeviceWithMapping } from "@/utils/mesh-device-utils";
import {
  enhanceDevicesWithMapping,
  sortDevicesForHierarchicalDisplay,
} from "@/utils/mesh-device-utils";
import { FAB } from "@/components/ui/FAB";
import { useHardwareModals } from "@/hooks/useHardwareModals";
import { useHardwareTabNavigation } from "@/hooks/useHardwareTabNavigation";
import { useSearch } from "@/hooks/useSearch";
import { useMeshDeviceMapping } from "@/hooks/useMeshDeviceMapping";

// Components
import HardwareTabs from "./components/HardwareTabs";
import HardwareSearchBar from "./components/HardwareSearchBar";
import DevicesTab from "./components/DevicesTab";
import PumpsTab from "./components/PumpsTab";
import DeviceDetailModal from "./components/DeviceDetailModal";
import PumpDetailModal from "./components/PumpDetailModal";
import ManageNetworkModal from "./components/ManageNetworkModal";
import AssignToLICModal from "./components/AssignToLICModal";

function HardwarePage({ activeTab }: { activeTab: string }) {
  const setBackButton = useSetAtom(appShell.backButtonAtom);
  const propertyDevices = useAtomValue(propertyDevicesAtom);
  const selectedProperty = useAtomValue(selectedPropertyAtom);

  // Custom hooks
  const { activeTab: currentTab, setActiveTab } = useHardwareTabNavigation(
    activeTab as "devices" | "pumps"
  );
  const { searchQuery, setSearchQuery } = useSearch();
  const { removeDeviceFromLIC, addDevicesToLIC } = useMeshDeviceMapping();
  const {
    isDeviceModalOpen,
    isPumpModalOpen,
    isManageNetworkModalOpen,
    isAssignToLICModalOpen,
    selectedDevice,
    selectedPump,
    selectedEnhancedDevice,
    modalMode,
    openDeviceModal,
    closeDeviceModal,
    openPumpModal,
    closePumpModal,
    openManageNetworkModal,
    closeManageNetworkModal,
    openAssignToLICModal,
    closeAssignToLICModal,
    setSelectedEnhancedDevice,
  } = useHardwareModals();

  useEffect(() => {
    setBackButton(false);
  }, [setBackButton]);

  // Enhanced devices with mapping information
  const enhancedDevices = useMemo(() => {
    const enhanced = enhanceDevicesWithMapping(propertyDevices);
    return sortDevicesForHierarchicalDisplay(enhanced);
  }, [propertyDevices]);

  // Sync selectedEnhancedDevice when enhancedDevices change
  useEffect(() => {
    if (selectedEnhancedDevice) {
      const updated = enhancedDevices.find(
        (d) => d.id === selectedEnhancedDevice.id
      );
      if (updated) {
        setSelectedEnhancedDevice(updated);
      }
    }
  }, [enhancedDevices, selectedEnhancedDevice, setSelectedEnhancedDevice]);

  // Available LICs for assignment
  const availableLICs = useMemo(() => {
    return enhancedDevices.filter((d) => d.isLIC);
  }, [enhancedDevices]);

  // All mesh devices for adding to LIC networks (including mapped ones for reassignment)
  const allMeshDevices = useMemo(() => {
    return enhancedDevices.filter((d) => d.isMesh);
  }, [enhancedDevices]);

  const handleDeviceClick = (device: DeviceWithMapping) => {
    openDeviceModal(device.device, device, "edit");
  };

  const handleManageNetwork = (device: DeviceWithMapping) => {
    openManageNetworkModal(device);
  };

  const handleEditLIC = (licDevice: DeviceWithMapping) => {
    closeManageNetworkModal();
    openDeviceModal(licDevice.device, licDevice, "edit");
  };

  const handleAddDevicesToLIC = async (
    items: Array<{
      meshDeviceId: string;
      licDeviceId: string;
      startDate: string;
    }>
  ) => {
    await addDevicesToLIC(items, enhancedDevices);
  };

  const handleRemoveDeviceFromLIC = async (deviceId: string) => {
    await removeDeviceFromLIC(deviceId, enhancedDevices);
  };

  const handleOpenAssignToLICFromDetail = (device: DeviceWithMapping) => {
    openAssignToLICModal(device);
  };

  const handlePumpClick = (pump: AUTWaterPump) => {
    openPumpModal(pump, "edit");
  };

  const handleAddDevice = () => {
    openDeviceModal(null, null, "create");
  };

  const handleAddPump = () => {
    openPumpModal(null, "create");
  };

  return (
    <div className="px-6 pb-4">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Hardware</h1>

      <HardwareTabs activeTab={currentTab} onTabChange={setActiveTab} />

      <HardwareSearchBar
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
      />

      {currentTab === "devices" && (
        <DevicesTab
          searchQuery={searchQuery}
          onDeviceClick={handleDeviceClick}
          onManageNetwork={handleManageNetwork}
        />
      )}

      {currentTab === "pumps" && (
        <PumpsTab searchQuery={searchQuery} onPumpClick={handlePumpClick} />
      )}

      <FAB
        onClick={currentTab === "devices" ? handleAddDevice : handleAddPump}
        className="bottom-20 right-4"
      />

      {isDeviceModalOpen && (
        <DeviceDetailModal
          isOpen={isDeviceModalOpen}
          onClose={closeDeviceModal}
          device={selectedDevice}
          enhancedDevice={selectedEnhancedDevice}
          mode={modalMode}
          onOpenAssignToLIC={handleOpenAssignToLICFromDetail}
          propertyData={selectedProperty}
        />
      )}

      {isPumpModalOpen && (
        <PumpDetailModal
          isOpen={isPumpModalOpen}
          onClose={closePumpModal}
          pump={selectedPump}
          mode={modalMode}
        />
      )}

      {selectedEnhancedDevice && (
        <ManageNetworkModal
          isOpen={isManageNetworkModalOpen}
          onClose={closeManageNetworkModal}
          licDevice={selectedEnhancedDevice}
          availableDevices={allMeshDevices}
          onAddDevices={handleAddDevicesToLIC}
          onRemoveDevice={handleRemoveDeviceFromLIC}
          onEditLIC={handleEditLIC}
        />
      )}

      {isAssignToLICModalOpen && (
        <AssignToLICModal
          isOpen={isAssignToLICModalOpen}
          onClose={closeAssignToLICModal}
          meshDevice={selectedEnhancedDevice}
          availableLICs={availableLICs}
        />
      )}
    </div>
  );
}

export default HardwarePage;
