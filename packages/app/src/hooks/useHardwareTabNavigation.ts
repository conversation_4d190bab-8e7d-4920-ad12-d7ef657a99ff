import { useState } from "react";

const HARDWARE_TABS = ["devices", "pumps"] as const;
type HardwareTab = (typeof HARDWARE_TABS)[number];

export function useHardwareTabNavigation(defaultTab: HardwareTab = "devices") {
  const [activeTab, setActiveTab] = useState<HardwareTab>(defaultTab);

  const validActiveTab =
    activeTab && HARDWARE_TABS.includes(activeTab) ? activeTab : defaultTab;

  return {
    activeTab: validActiveTab,
    setActiveTab,
  };
}
