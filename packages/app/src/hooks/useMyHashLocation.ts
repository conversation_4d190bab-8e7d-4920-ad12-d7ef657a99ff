import { useSyncExternalStore, useCallback } from "react";
import { BaseSearchHook } from "wouter";

const eventPopstate = "popstate";
const eventPushState = "pushState";
const eventReplaceState = "replaceState";
const eventHashchange = "hashchange";
const events = [
  eventPopstate,
  eventPushState,
  eventReplaceState,
  eventHashchange,
];

const subscribeToLocationUpdates = (callback: () => void) => {
  for (const event of events) {
    addEventListener(event, callback);
  }
  return () => {
    for (const event of events) {
      removeEventListener(event, callback);
    }
  };
};

export const useLocationProperty = (
  getSnapshot: () => string,
  getServerSnapshot?: (() => string) | undefined
) =>
  useSyncExternalStore(
    subscribeToLocationUpdates,
    getSnapshot,
    getServerSnapshot
  );

export function useMyHashLocation(): [
  string,
  (path: string, options?: { replace?: boolean }) => void
] {
  const getSnapshot = useCallback(() => {
    const hash = window.location.hash.slice(1); // Remove the #

    if (!hash) return "/";

    // Extract only the path part (before '?')
    const pathOnly = hash.split("?")[0];

    // Ensure we have a clean path
    return pathOnly || "/";
  }, []);

  const getServerSnapshot = useCallback(() => {
    return "/";
  }, []);

  const currentPath = useLocationProperty(getSnapshot, getServerSnapshot);

  const setPath = useCallback(
    (path: string, options?: { replace?: boolean }) => {
      // Remove any existing query params from the input path
      const [cleanPath, search] = path.split("?");
      const newHash = `#${cleanPath}`;
      console.log("setPath - newHash:", { path, cleanPath, search, newHash });
      if (options?.replace) {
        // Preserve existing query params if any
        const currentSearch = window.location.search;
        window.history.replaceState(null, "", path);
        window.dispatchEvent(new HashChangeEvent("hashchange"));
      } else {
        window.location.hash = path;
      }
    },
    []
  );

  return [currentPath, setPath];
}

export const currentSearch = () => {
  const search = location.search;
  const hashSearch = location.hash.split("?")[1] || "";
  const fullSearch = [search, hashSearch].filter(Boolean).join("&");
  console.log("useMySearchHook - fullSearch:", {
    search,
    hashSearch,
    fullSearch,
  });
  return fullSearch ? `?${fullSearch}` : "";
};

export const useMySearchHook: BaseSearchHook = ({ ssrSearch = "" } = {}) =>
  useLocationProperty(currentSearch, () => ssrSearch);
