import { describe, it, beforeEach, afterEach, expect } from "bun:test";
import { Knex } from "knex";
import {
  begin,
  rollbackAndDestroy,
  createKnex,
  expectRejectWithinSavepoint,
} from "./helpers/db";
import {
  daysAgo,
  plusSeconds,
  insertUser,
  insertAccount,
  insertProperty,
  insertDevice,
  insertPropertyDevice,
  insertMeshMapping,
  getCurrentMeshMappingId,
} from "./helpers/fixtures";

let knex: Knex;
let trx: Knex.Transaction;

function closeMs(a: Date, b: Date, toleranceMs = 10) {
  return Math.abs(a.getTime() - b.getTime()) <= toleranceMs;
}

describe("mesh_device_mapping", () => {
  beforeEach(async () => {
    knex = createKnex();
    trx = await begin(knex);
  });

  afterEach(async () => {
    await rollbackAndDestroy(trx);
  });

  async function setupPropertyWithDevices() {
    const userId = await insertUser(trx);
    const accountId = await insertAccount(trx, userId);
    const propertyId = await insertProperty(trx, accountId);

    const licDev = await insertDevice(trx, "LIC");
    const meshDev1 = await insertDevice(trx, "WPC-PL10");
    const meshDev2 = await insertDevice(trx, "VC");

    const now = new Date();
    // Create property devices with wider time range to accommodate mesh mappings
    // Start from 15 days ago to allow mesh mappings that go back to 10 days ago
    const licPd = await insertPropertyDevice(
      trx,
      licDev,
      propertyId,
      daysAgo(15),
      null
    );
    const meshPd1 = await insertPropertyDevice(
      trx,
      meshDev1,
      propertyId,
      daysAgo(15),
      null
    );
    const meshPd2 = await insertPropertyDevice(
      trx,
      meshDev2,
      propertyId,
      daysAgo(15),
      null
    );

    return { propertyId, licPd, meshPd1, meshPd2, now };
  }

  it("creates mapping and updates current mapping (happy path)", async () => {
    const { licPd, meshPd1 } = await setupPropertyWithDevices();

    const start = daysAgo(1);
    const id = await insertMeshMapping(trx, meshPd1, licPd, start, null);
    expect(id).toBeDefined();

    // AFTER trigger should update current mapping automatically
    const currentId = await getCurrentMeshMappingId(trx, meshPd1);
    expect(currentId).toBeString();
    expect(currentId).toEqual(id);
  });

  it("picks open-ended active mapping over older closed one", async () => {
    const { licPd, meshPd1 } = await setupPropertyWithDevices();

    const oldStart = daysAgo(10);
    const oldEnd = daysAgo(5);
    await insertMeshMapping(trx, meshPd1, licPd, oldStart, oldEnd);

    const activeStart = daysAgo(2);
    const openId = await insertMeshMapping(
      trx,
      meshPd1,
      licPd,
      activeStart,
      null
    );

    // AFTER trigger should update current mapping automatically
    const currentId = await getCurrentMeshMappingId(trx, meshPd1);
    expect(currentId).toEqual(openId);
  });

  it("rejects mapping if mesh and LIC properties differ", async () => {
    const userId = await insertUser(trx);
    const accountId = await insertAccount(trx, userId);
    const propA = await insertProperty(trx, accountId, "PropA");
    const propB = await insertProperty(trx, accountId, "PropB");

    const licDev = await insertDevice(trx, "LIC");
    const meshDev = await insertDevice(trx, "WPC-PL10");

    const licPd = await insertPropertyDevice(
      trx,
      licDev,
      propA,
      daysAgo(5),
      null
    );
    const meshPd = await insertPropertyDevice(
      trx,
      meshDev,
      propB,
      daysAgo(5),
      null
    );

    await expectRejectWithinSavepoint(trx, async () => {
      await insertMeshMapping(trx, meshPd, licPd, daysAgo(1), null);
    });
  });

  it("rejects mapping when lic device is not LIC", async () => {
    const userId = await insertUser(trx);
    const accountId = await insertAccount(trx, userId);
    const propertyId = await insertProperty(trx, accountId);

    const notLicDev = await insertDevice(trx, "VC");
    const meshDev = await insertDevice(trx, "WPC-PL10");

    const licPd = await insertPropertyDevice(
      trx,
      notLicDev,
      propertyId,
      daysAgo(2),
      null
    );
    const meshPd = await insertPropertyDevice(
      trx,
      meshDev,
      propertyId,
      daysAgo(2),
      null
    );

    await expectRejectWithinSavepoint(trx, async () => {
      await insertMeshMapping(trx, meshPd, licPd, daysAgo(1), null);
    });
  });

  it("rejects mapping when mesh device model is not mesh-capable", async () => {
    const userId = await insertUser(trx);
    const accountId = await insertAccount(trx, userId);
    const propertyId = await insertProperty(trx, accountId);

    const licDev = await insertDevice(trx, "LIC");
    const nonMeshDev = await insertDevice(trx, "LIC"); // attempt to use LIC as mesh
    const licPd = await insertPropertyDevice(
      trx,
      licDev,
      propertyId,
      daysAgo(2),
      null
    );
    const meshPd = await insertPropertyDevice(
      trx,
      nonMeshDev,
      propertyId,
      daysAgo(2),
      null
    );

    await expectRejectWithinSavepoint(trx, async () => {
      await insertMeshMapping(trx, meshPd, licPd, daysAgo(1), null);
    });
  });

  it("rejects when mesh PD is not active now", async () => {
    const { licPd } = await setupPropertyWithDevices();
    const userId = await insertUser(trx);
    const accountId = await insertAccount(trx, userId);
    const propertyId = await insertProperty(trx, accountId);

    const meshDev = await insertDevice(trx, "WPC-PL10");
    // Not active now: ended yesterday
    const meshPd = await insertPropertyDevice(
      trx,
      meshDev,
      propertyId,
      daysAgo(10),
      daysAgo(1)
    );

    await expectRejectWithinSavepoint(trx, async () => {
      await insertMeshMapping(trx, meshPd, licPd, daysAgo(1), null);
    });
  });

  it("rejects when LIC PD is not active now", async () => {
    const { meshPd1 } = await setupPropertyWithDevices();
    const userId = await insertUser(trx);
    const accountId = await insertAccount(trx, userId);
    const propertyId = await insertProperty(trx, accountId);

    const licDev = await insertDevice(trx, "LIC");
    // Not active now: starts in the future
    const licPd = await insertPropertyDevice(
      trx,
      licDev,
      propertyId,
      plusSeconds(new Date(), 3600),
      null
    );

    await expectRejectWithinSavepoint(trx, async () => {
      await insertMeshMapping(trx, meshPd1, licPd, daysAgo(1), null);
    });
  });

  it("accommodation: existing starts before new -> truncate existing end", async () => {
    const { licPd, meshPd1 } = await setupPropertyWithDevices();

    const existingStart = daysAgo(10);
    const existingEnd = daysAgo(2);
    const existingId = await insertMeshMapping(
      trx,
      meshPd1,
      licPd,
      existingStart,
      existingEnd
    );

    const newStart = daysAgo(3);
    const newEnd = daysAgo(1);
    await insertMeshMapping(trx, meshPd1, licPd, newStart, newEnd);

    const row = await trx("mesh_device_mapping")
      .where({ id: existingId })
      .first();
    expect(closeMs(new Date(row.end_date), new Date(newStart))).toBeTrue();
  });

  it("accommodation: existing ends after new -> move existing start to new end + 1s", async () => {
    const { licPd, meshPd1 } = await setupPropertyWithDevices();

    const existingStart = daysAgo(1);
    const existingEnd = plusSeconds(new Date(), 86400); // +1 day
    const existingId = await insertMeshMapping(
      trx,
      meshPd1,
      licPd,
      existingStart,
      existingEnd
    );

    const newStart = daysAgo(2);
    const newEnd = daysAgo(1);
    await insertMeshMapping(trx, meshPd1, licPd, newStart, newEnd);

    const row = await trx("mesh_device_mapping")
      .where({ id: existingId })
      .first();
    const expected = plusSeconds(newEnd, 1);
    expect(closeMs(new Date(row.start_date), expected)).toBeTrue();
  });

  it("accommodation: existing contained within new -> error", async () => {
    const { licPd, meshPd1 } = await setupPropertyWithDevices();

    const existingId = await insertMeshMapping(
      trx,
      meshPd1,
      licPd,
      daysAgo(5),
      daysAgo(3)
    );
    await expectRejectWithinSavepoint(trx, async () => {
      await insertMeshMapping(trx, meshPd1, licPd, daysAgo(6), daysAgo(2));
    });
    // existing should remain unmodified
    const row = await trx("mesh_device_mapping")
      .where({ id: existingId })
      .first();
    expect(row).toBeDefined();
  });

  it("accommodation: existing contains new -> split existing in two", async () => {
    const { licPd, meshPd1 } = await setupPropertyWithDevices();

    const origEnd = daysAgo(1);
    const existingId = await insertMeshMapping(
      trx,
      meshPd1,
      licPd,
      daysAgo(10),
      origEnd
    );

    const newStart = daysAgo(5);
    const newEnd = daysAgo(3);
    await insertMeshMapping(trx, meshPd1, licPd, newStart, newEnd);

    // Left piece should end at newStart
    const left = await trx("mesh_device_mapping")
      .where({ id: existingId })
      .first();
    expect(closeMs(new Date(left.end_date), new Date(newStart))).toBeTrue();

    // Right piece should start at newEnd + 1s and end at origEnd
    const candidates = await trx("mesh_device_mapping").where({
      mesh_property_device: meshPd1,
      lic_property_device: licPd,
    });
    const right = candidates.find(
      (r: any) =>
        closeMs(new Date(r.start_date), plusSeconds(newEnd, 1)) &&
        closeMs(new Date(r.end_date), new Date(origEnd))
    );
    expect(right).toBeDefined();
  });

  it("accommodation: new with infinite end and overlapping right existing -> raises error", async () => {
    const { licPd, meshPd1 } = await setupPropertyWithDevices();

    const existingStart = daysAgo(1);
    const existingEnd = plusSeconds(new Date(), 86400); // +1 day
    await insertMeshMapping(trx, meshPd1, licPd, existingStart, existingEnd);

    await expectRejectWithinSavepoint(trx, async () => {
      await insertMeshMapping(trx, meshPd1, licPd, daysAgo(2), null);
    });
  });

  it("function: update current mapping with empty array -> no error, no change", async () => {
    const { meshPd1 } = await setupPropertyWithDevices();

    const currentBefore = await getCurrentMeshMappingId(trx, meshPd1);
    await trx.raw(
      `SELECT im_update_current_mesh_device_mapping(ARRAY[]::uuid[], now())`
    );
    const currentAfter = await getCurrentMeshMappingId(trx, meshPd1);
    expect(currentAfter).toEqual(currentBefore);
  });

  it("function: reference_date picks historical at that time", async () => {
    const { licPd, meshPd1 } = await setupPropertyWithDevices();

    const histStart = daysAgo(10);
    const histEnd = daysAgo(5);
    const histId = await insertMeshMapping(
      trx,
      meshPd1,
      licPd,
      histStart,
      histEnd
    );

    const activeId = await insertMeshMapping(
      trx,
      meshPd1,
      licPd,
      daysAgo(3),
      null
    );

    await trx.raw(
      `SELECT im_update_current_mesh_device_mapping(ARRAY[?]::uuid[], ?)`,
      [meshPd1, histStart]
    );
    const current = await getCurrentMeshMappingId(trx, meshPd1);
    expect(current).toEqual(histId);

    await trx.raw(
      `SELECT im_update_current_mesh_device_mapping(ARRAY[?]::uuid[], now())`,
      [meshPd1]
    );
    const currentNow = await getCurrentMeshMappingId(trx, meshPd1);
    expect(currentNow).toEqual(activeId);
  });

  it("function: when no active record at reference_date -> current becomes NULL", async () => {
    const { licPd, meshPd1 } = await setupPropertyWithDevices();

    await insertMeshMapping(trx, meshPd1, licPd, daysAgo(10), daysAgo(9));

    await trx.raw(
      `SELECT im_update_current_mesh_device_mapping(ARRAY[?]::uuid[], now())`,
      [meshPd1]
    );

    const current = await getCurrentMeshMappingId(trx, meshPd1);
    expect(current).toBeNull();
  });

  it("update trigger: changing start_date invokes accommodation", async () => {
    const { licPd, meshPd1 } = await setupPropertyWithDevices();

    const id1 = await insertMeshMapping(
      trx,
      meshPd1,
      licPd,
      daysAgo(5),
      daysAgo(3)
    );
    const id2 = await insertMeshMapping(trx, meshPd1, licPd, daysAgo(2), null);

    // Move id2 start into id1 period -> should truncate id1 or throw based on logic
    await trx("mesh_device_mapping")
      .where({ id: id2 })
      .update({ start_date: daysAgo(4) });

    const r1 = await trx("mesh_device_mapping").where({ id: id1 }).first();
    expect(closeMs(new Date(r1.end_date), new Date(daysAgo(4)))).toBeTrue();
  });

  it("update trigger: changing end_date invokes accommodation", async () => {
    const { licPd, meshPd1 } = await setupPropertyWithDevices();

    const id1 = await insertMeshMapping(
      trx,
      meshPd1,
      licPd,
      daysAgo(5),
      daysAgo(3)
    );
    const id2 = await insertMeshMapping(trx, meshPd1, licPd, daysAgo(2), null);

    // Extend id1 end into id2 start -> should shift id2.start to id1.end + 1s
    const newEnd = daysAgo(1);
    await trx("mesh_device_mapping")
      .where({ id: id1 })
      .update({ end_date: newEnd });

    const r2 = await trx("mesh_device_mapping").where({ id: id2 }).first();
    const expectedStart = plusSeconds(newEnd, 1);
    expect(closeMs(new Date(r2.start_date), expectedStart)).toBeTrue();
  });

  it("update trigger: changing lic_property_device to create overlap fails", async () => {
    const { licPd, meshPd1 } = await setupPropertyWithDevices();
    const otherLicDev = await insertDevice(trx, "LIC");
    const { propertyId } = await trx("property_device")
      .where({ id: meshPd1 })
      .first()
      .then(async () => {
        const u = await trx("directus_users").first();
        const acc = await insertAccount(trx, u.id);
        const prop = await insertProperty(trx, acc);
        return { propertyId: prop };
      });

    const otherLicPd = await insertPropertyDevice(
      trx,
      otherLicDev,
      propertyId,
      daysAgo(5),
      null
    );

    await insertMeshMapping(trx, meshPd1, licPd, daysAgo(5), null);

    await expectRejectWithinSavepoint(trx, async () => {
      await trx("mesh_device_mapping")
        .where({ mesh_property_device: meshPd1 })
        .update({ lic_property_device: otherLicPd });
    });
  });

  it("indexes exist for faster lookups", async () => {
    const idxRows = await trx
      .select("indexname")
      .from("pg_indexes")
      .whereIn("indexname", [
        "mesh_device_mapping_mesh_start_idx",
        "mesh_device_mapping_mesh_end_idx",
        "property_device_current_mesh_idx",
      ]);

    const names = idxRows.map((r: any) => r.indexname);
    expect(names).toContain("mesh_device_mapping_mesh_start_idx");
    expect(names).toContain("mesh_device_mapping_mesh_end_idx");
    expect(names).toContain("property_device_current_mesh_idx");
  });
});
