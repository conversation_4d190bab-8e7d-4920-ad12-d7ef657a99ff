-- Test script to verify the mesh constraint trigger fixes
-- This script tests various scenarios with NULL device assignments

-- Test 1: Create a project with NULL LIC and NULL irrigation pump (should succeed)
BEGIN;
INSERT INTO project (id, name, property, localized_irrigation_controller, irrigation_water_pump)
VALUES (
  gen_random_uuid(),
  'Test Project - No LIC',
  (SELECT id FROM property LIMIT 1),
  NULL,
  NULL
);
ROLLBACK;

-- Test 2: Try to create a project with NULL LIC but non-NULL irrigation pump (should fail)
BEGIN;
DO $$
DECLARE
  test_project_id UUID := gen_random_uuid();
  test_property_id UUID;
  test_pump_id UUID;
BEGIN
  -- Get a property and water pump for testing
  SELECT id INTO test_property_id FROM property LIMIT 1;
  SELECT id INTO test_pump_id FROM water_pump LIMIT 1;
  
  -- This should fail with our new validation
  INSERT INTO project (id, name, property, localized_irrigation_controller, irrigation_water_pump)
  VALUES (
    test_project_id,
    'Test Project - Invalid State',
    test_property_id,
    NULL,
    test_pump_id
  );
  
  RAISE NOTICE 'ERROR: This should have failed but did not!';
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'SUCCESS: Correctly prevented project with NULL LIC but non-NULL irrigation pump: %', SQLERRM;
END;
$$;
ROLLBACK;

-- Test 3: Create a sector with NULL valve controller (should succeed)
BEGIN;
DO $$
DECLARE
  test_sector_id UUID := gen_random_uuid();
  test_project_id UUID;
BEGIN
  -- Get a project for testing
  SELECT id INTO test_project_id FROM project LIMIT 1;
  
  INSERT INTO sector (id, name, project, valve_controller, valve_controller_output)
  VALUES (
    test_sector_id,
    'Test Sector - No Valve Controller',
    test_project_id,
    NULL,
    NULL
  );
  
  RAISE NOTICE 'SUCCESS: Created sector with NULL valve controller';
END;
$$;
ROLLBACK;

-- Test 4: Try to create a sector with valve controller when project has NULL LIC (should fail)
BEGIN;
DO $$
DECLARE
  test_sector_id UUID := gen_random_uuid();
  test_project_id UUID;
  test_device_id UUID;
BEGIN
  -- Create a test project with NULL LIC
  test_project_id := gen_random_uuid();
  INSERT INTO project (id, name, property, localized_irrigation_controller, irrigation_water_pump)
  VALUES (
    test_project_id,
    'Test Project for Sector Test',
    (SELECT id FROM property LIMIT 1),
    NULL,
    NULL
  );
  
  -- Get a device for testing
  SELECT id INTO test_device_id FROM device WHERE device_model LIKE '%VC%' LIMIT 1;
  
  -- This should fail
  INSERT INTO sector (id, name, project, valve_controller, valve_controller_output)
  VALUES (
    test_sector_id,
    'Test Sector - Should Fail',
    test_project_id,
    test_device_id,
    1
  );
  
  RAISE NOTICE 'ERROR: This should have failed but did not!';
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'SUCCESS: Correctly prevented sector with valve controller when project has no LIC: %', SQLERRM;
END;
$$;
ROLLBACK;

-- Test 5: Verify existing data still works
BEGIN;
DO $$
DECLARE
  existing_project_count INTEGER;
  existing_sector_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO existing_project_count FROM project;
  SELECT COUNT(*) INTO existing_sector_count FROM sector;
  
  RAISE NOTICE 'Existing projects: %, Existing sectors: %', existing_project_count, existing_sector_count;
  RAISE NOTICE 'SUCCESS: Existing data is accessible and triggers are not breaking existing functionality';
END;
$$;
ROLLBACK;

-- Summary
SELECT 'Trigger fix tests completed. Check the notices above for results.' AS test_summary;
