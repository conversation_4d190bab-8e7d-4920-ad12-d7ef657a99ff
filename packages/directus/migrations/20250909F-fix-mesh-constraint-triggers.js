/**
 * Migration to fix mesh constraint triggers for nullable device fields
 * This addresses incompatibilities introduced by allowing NULL values for:
 * - project.localized_irrigation_controller
 * - sector.valve_controller and valve_controller_output  
 * - project.irrigation_water_pump
 */
export async function up(knex) {
  // Update check_sector_mesh function to handle NULL valve_controller
  await knex.raw(`
    CREATE OR REPLACE FUNCTION check_sector_mesh()
    RETURNS TRIGGER AS $$
    DECLARE
      v_project_lic_property_device_id UUID;
      v_vc_lic_property_device_id UUID;
    BEGIN
      -- Only validate if valve_controller is not NULL
      IF NEW.valve_controller IS NOT NULL THEN
        -- Get Project's LIC property_device ID
        SELECT pd.id INTO v_project_lic_property_device_id
        FROM project p
        JOIN property_device pd ON pd.device = p.localized_irrigation_controller AND pd.end_date IS NULL
        WHERE p.id = NEW.project;

        -- If project has a LIC, validate mesh network constraint
        IF v_project_lic_property_device_id IS NOT NULL THEN
          -- Get Valve Controller's LIC property_device ID
          SELECT get_lic_for_device(NEW.valve_controller) INTO v_vc_lic_property_device_id;

          IF v_project_lic_property_device_id IS DISTINCT FROM v_vc_lic_property_device_id THEN
            RAISE EXCEPTION 'Valve Controller must be in the same mesh network as the project LIC.';
          END IF;
        ELSE
          -- If project has no LIC, valve controller should not be assigned
          RAISE EXCEPTION 'Cannot assign valve controller to sector when project has no LIC assigned.';
        END IF;
      END IF;
      
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
  `);

  // Update check_project_mesh function to handle irrigation_water_pump dependency
  await knex.raw(`
    CREATE OR REPLACE FUNCTION check_project_mesh()
    RETURNS TRIGGER AS $$
    DECLARE
      v_project_lic_property_device_id UUID;
      v_irrigation_pump_lic_property_device_id UUID;
      v_fertigation_pump_lic_property_device_id UUID;
    BEGIN
      -- Validate irrigation_water_pump dependency on localized_irrigation_controller
      IF NEW.localized_irrigation_controller IS NULL AND NEW.irrigation_water_pump IS NOT NULL THEN
        RAISE EXCEPTION 'irrigation_water_pump must be NULL when localized_irrigation_controller is NULL.';
      END IF;

      -- If LIC is assigned, validate mesh network constraints
      IF NEW.localized_irrigation_controller IS NOT NULL THEN
        -- Get the LIC's property_device ID
        SELECT id INTO v_project_lic_property_device_id
        FROM property_device
        WHERE device = NEW.localized_irrigation_controller AND end_date IS NULL;

        -- Check irrigation pump
        IF NEW.irrigation_water_pump IS NOT NULL THEN
          SELECT get_lic_for_device(wp.water_pump_controller) INTO v_irrigation_pump_lic_property_device_id
          FROM water_pump wp WHERE wp.id = NEW.irrigation_water_pump;
          IF v_project_lic_property_device_id IS DISTINCT FROM v_irrigation_pump_lic_property_device_id THEN
            RAISE EXCEPTION 'Irrigation pump must be in the same mesh network as the project LIC.';
          END IF;
        END IF;

        -- Check fertigation pump
        IF NEW.fertigation_water_pump IS NOT NULL THEN
          SELECT get_lic_for_device(wp.water_pump_controller) INTO v_fertigation_pump_lic_property_device_id
          FROM water_pump wp WHERE wp.id = NEW.fertigation_water_pump;
          IF v_project_lic_property_device_id IS DISTINCT FROM v_fertigation_pump_lic_property_device_id THEN
            RAISE EXCEPTION 'Fertigation pump must be in the same mesh network as the project LIC.';
          END IF;
        END IF;
      END IF;
      
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
  `);
}

export async function down(knex) {
  // Restore original check_sector_mesh function
  await knex.raw(`
    CREATE OR REPLACE FUNCTION check_sector_mesh()
    RETURNS TRIGGER AS $$
    DECLARE
      v_project_lic_property_device_id UUID;
      v_vc_lic_property_device_id UUID;
    BEGIN
      -- Get Project's LIC property_device ID
      SELECT pd.id INTO v_project_lic_property_device_id
      FROM project p
      JOIN property_device pd ON pd.device = p.localized_irrigation_controller AND pd.end_date IS NULL
      WHERE p.id = NEW.project;

      IF v_project_lic_property_device_id IS NOT NULL THEN
        -- Get Valve Controller's LIC property_device ID
        SELECT get_lic_for_device(NEW.valve_controller) INTO v_vc_lic_property_device_id;

        IF v_project_lic_property_device_id IS DISTINCT FROM v_vc_lic_property_device_id THEN
          RAISE EXCEPTION 'Valve Controller must be in the same mesh network as the project LIC.';
        END IF;
      END IF;
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
  `);

  // Restore original check_project_mesh function
  await knex.raw(`
    CREATE OR REPLACE FUNCTION check_project_mesh()
    RETURNS TRIGGER AS $$
    DECLARE
      v_project_lic_property_device_id UUID;
      v_irrigation_pump_lic_property_device_id UUID;
      v_fertigation_pump_lic_property_device_id UUID;
    BEGIN
      IF NEW.localized_irrigation_controller IS NOT NULL THEN
        -- Get the LIC's property_device ID
        SELECT id INTO v_project_lic_property_device_id
        FROM property_device
        WHERE device = NEW.localized_irrigation_controller AND end_date IS NULL;

        -- Check irrigation pump
        IF NEW.irrigation_water_pump IS NOT NULL THEN
          SELECT get_lic_for_device(wp.water_pump_controller) INTO v_irrigation_pump_lic_property_device_id
          FROM water_pump wp WHERE wp.id = NEW.irrigation_water_pump;
          IF v_project_lic_property_device_id IS DISTINCT FROM v_irrigation_pump_lic_property_device_id THEN
            RAISE EXCEPTION 'Irrigation pump must be in the same mesh network as the project LIC.';
          END IF;
        END IF;

        -- Check fertigation pump
        IF NEW.fertigation_water_pump IS NOT NULL THEN
          SELECT get_lic_for_device(wp.water_pump_controller) INTO v_fertigation_pump_lic_property_device_id
          FROM water_pump wp WHERE wp.id = NEW.fertigation_water_pump;
          IF v_project_lic_property_device_id IS DISTINCT FROM v_fertigation_pump_lic_property_device_id THEN
            RAISE EXCEPTION 'Fertigation pump must be in the same mesh network as the project LIC.';
          END IF;
        END IF;
      END IF;
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
  `);
}
