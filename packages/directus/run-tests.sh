#!/bin/bash

# Script to run tests with proper database setup
set -e

# Test database configuration
TEST_DB_NAME="test_db"

echo "Setting up test database..."
# Create/recreate test database
./scripts/create-docker-db.sh $TEST_DB_NAME --recreate

echo "Running tests..."
# Set environment variables for test database connection
export TEST_DB_HOST="localhost"
export TEST_DB_PORT="5432"
export TEST_DB_USER="postgres"
export TEST_DB_PASSWORD=""
export TEST_DB_DATABASE="$TEST_DB_NAME"

# Run the specific test
cd tests
bun test mesh_constraints.test.ts

echo "Tests completed!"
